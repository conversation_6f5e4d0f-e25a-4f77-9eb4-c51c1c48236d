sequenceDiagram
    participant User
    participant ComptrollerProxy
    participant ComptrollerLib
    participant FeeManager
    participant PolicyManager
    participant ValueInterpreter
    participant VaultProxy

    User->>ComptrollerProxy: buyShares()
    ComptrollerProxy->>ComptrollerLib: buyShares()
    
    ComptrollerLib->>ValueInterpreter: calcGav()
    ValueInterpreter-->>ComptrollerLib: GAV value
    
    ComptrollerLib->>FeeManager: invokeHook(PreBuyShares)
    FeeManager-->>ComptrollerLib: fees processed
    
    ComptrollerLib->>VaultProxy: payProtocolFee()
    ComptrollerLib->>VaultProxy: mintShares()
    
    ComptrollerLib->>PolicyManager: validatePolicies(PostBuyShares)
    PolicyManager-->>ComptrollerLib: validation result
    
    ComptrollerLib-->>User: shares issued