sequenceDiagram
    participant User
    participant ComptrollerLib
    participant FeeManager
    participant ValueInterpreters
    participant VaultProxy
    participant PolicyManager

    User->>ComptrollerLib: redeemSharesForSpecificAssets()
    
    ComptrollerLib->>ValueInterpreter: calcGav()
    ValueInterpreter-->>ComptrollerLib: GAV value
    
    ComptrollerLib->>FeeManager: invokeHook(PreRedeemShares)
    FeeManager-->>ComptrollerLib: exit fees processed
    
    ComptrollerLib->>VaultProxy: payProtocolFee()
    
    ComptrollerLib->>ValueInterpreter: calcCanonicalAssetValue()
    ValueInterpreter-->>ComptrollerLib: asset amounts
    
    ComptrollerLib->>VaultProxy: withdrawAssetTo()
    ComptrollerLib->>VaultProxy: burnShares()
    
    ComptrollerLib->>PolicyManager: validatePolicies(RedeemSharesForSpecificAssets)
    PolicyManager-->>ComptrollerLib: validation result