sequenceDiagram
    participant User
    participant ComptrollerLib
    participant IntegrationManager
    participant Adapter
    participant VaultProxy
    participant DEX

    User->>ComptrollerLib: callOnExtension(IntegrationManager)
    ComptrollerLib->>IntegrationManager: receiveCallFromComptroller()
    
    IntegrationManager->>Adapter: parseAssetsForAction()
    Adapter-->>IntegrationManager: spend/incoming assets
    
    IntegrationManager->>VaultProxy: withdrawAssetTo(Adapter)
    IntegrationManager->>Adapter: trade()
    Adapter->>DEX: execute trade
    
    Adapter->>VaultProxy: transfer incoming assets
    IntegrationManager->>VaultProxy: addTrackedAsset()