classDiagram
    class FeeManager {
        +invokeHook(hook, data, gav)
        +receiveCallFromComptroller()
        -__settleFees()
        -__updateFees()
    }
    
    class IFee {
        <<interface>>
        +settlesOnHook(hook)
        +settle()
        +update()
    }
    
    class ManagementFee {
        +settle() SettlementType.Mint
        +settlesOnHook() PreBuyShares, PreRedeemShares, Continuous
    }
    
    class PerformanceFee {
        +settle() SettlementType.Mint
        +settlesOnHook() PreBuyShares, PreRedeemShares, Continuous
    }
    
    class ExitRateBurnFee {
        +settle() SettlementType.Burn
        +settlesOnHook() PreRedeemShares
    }
    
    FeeManager --> IFee
    IFee <|-- ManagementFee
    IFee <|-- PerformanceFee
    IFee <|-- ExitRateBurnFee